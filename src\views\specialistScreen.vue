<!-- 专家出诊屏 -->
<template>
  <div class="page-container">
    <h1>专家出诊屏</h1>
  </div>
</template>

<script>
export default {
  name: "specialistScreen",
  data() {
    return {};
  },
  methods: {
    async getData() {
      const res = await this.$api.price.queryPrice();
      console.log("出诊接口", res);
      if (res.success) {
        console.log(11);
      } else {
        this.$message.error(res.message);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.page-container {
}
</style>
